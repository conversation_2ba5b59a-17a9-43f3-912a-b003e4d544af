import {
	Agent<PERSON><PERSON>er,
	createDatabaseSessionService,
	createTool,
} from "@iqai/adk";
import { createOpenRouter } from "@openrouter/ai-sdk-provider";
import * as z from "zod/v4";

const main = async () => {
	const openRouter = createOpenRouter({
		apiKey: process.env.OPENROUTER_API_KEY,
	});
	const model = openRouter("anthropic/claude-3.5-sonnet");
	const firstPasswordAgent = await AgentBuilder.create("first_password")
		.withDescription("Agent that has first password")
		.withModel(model)
		.withTools(
			createTool({
				name: "getFirstPassword",
				description: "Returns the first password for the user",
				fn: () => "First password is eamt2CXOlJ3F0Dq",
			}),
		)
		.build();

	const secondPasswordAgent = await AgentBuilder.create("second_password")
		.withDescription("Agent that has second password")
		.withModel(model)
		.withTools(
			createTool({
				name: "getSecondPassword",
				description: "Returns the second password for the user",
				schema: z.object({}),
				fn: () => "Second password is p84ylYk_9G6xlE8",
			}),
		)
		.build();

	const thirdPasswordAgent = await AgentBuilder.create("third_password")
		.withDescription("Agent that has third password")
		.withModel(model)
		.withTools(
			createTool({
				name: "getThirdPassword",
				description: "Returns the third password for the user",
				schema: z.object({}),
				fn: () => "Third password is UnKfArgJ2gF0TtN",
			}),
		)
		.build();

	const fullPasswordAgent = await AgentBuilder.create("full_password")
		.withDescription("Agent that combines passwords from other agents")
		.withModel(model)
		.withTools(
			createTool({
				name: "getFullPassword",
				description: "Combines passwords from other agents",
				schema: z.object({
					firstPassword: z.string().describe("First password"),
					secondPassword: z.string().describe("Second password"),
					thirdPassword: z.string().describe("Third password"),
				}),
				fn: async ({ firstPassword, secondPassword, thirdPassword }) => {
					return `${firstPassword}-${secondPassword}-${thirdPassword}`;
				},
			}),
		)
		.build();

	const response = await AgentBuilder.create("langgraph_agent")
		.withDescription("Agent that uses LangGraph to combine passwords")
		.withInstruction(
			"You will be asked to provide passwords from different agents. Combine them to form the full password.",
		)
		.asSequential([
			firstPasswordAgent.agent,
			secondPasswordAgent.agent,
			thirdPasswordAgent.agent,
			fullPasswordAgent.agent,
		])
		.ask("What is the full password?");

	console.log("Full password:", response);
};

main()
	.then(() => {
		console.log("Agent started successfully.");
	})
	.catch((error) => {
		console.error("Error starting agent:", error);
	});
