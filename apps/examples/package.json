{"name": "@iqai/examples", "version": "0.0.7", "description": "Agent Development Kit for TypeScript with multi-provider LLM support", "scripts": {"start": "tsx ./src/index.ts", "dev": "tsx ./src/index.ts"}, "private": true, "author": "IQAI", "license": "MIT", "dependencies": {"@ai-sdk/google": "^1.2.22", "@clack/prompts": "^0.11.0", "@google/genai": "^1.6.0", "@iqai/adk": "workspace:*", "@modelcontextprotocol/sdk": "^1.11.1", "dedent": "^1.6.0", "dotenv": "^16.5.0", "fastmcp": "^3.8.2", "uuid": "^11.1.0", "zod": "^3.24.4"}, "devDependencies": {"@iqai/tsconfig": "workspace:*", "@types/node": "^20.17.30", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "^5.3.2"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=22.0"}}